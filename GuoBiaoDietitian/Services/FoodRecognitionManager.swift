import Vision
import CoreML
import UIKit
import SwiftUI
import Combine

@MainActor
class FoodRecognitionManager: ObservableObject {
    @Published var isProcessing = false
    @Published var recognitionResults: [FoodRecognitionResult] = []
    @Published var errorMessage: String?
    
    private var cancellables = Set<AnyCancellable>()
    
    // 模拟的食物识别模型（实际项目中应该使用训练好的 Core ML 模型）
    private let mockFoodClassifications = [
        "apple": "苹果",
        "banana": "香蕉",
        "orange": "橙子",
        "rice": "米饭",
        "bread": "面包",
        "chicken": "鸡肉",
        "beef": "牛肉",
        "fish": "鱼",
        "broccoli": "西兰花",
        "carrot": "胡萝卜",
        "tomato": "西红柿",
        "potato": "土豆",
        "egg": "鸡蛋",
        "milk": "牛奶",
        "cheese": "奶酪"
    ]
    
    func recognizeFood(from image: UIImage) {
        isProcessing = true
        errorMessage = nil
        recognitionResults = []
        
        // 模拟处理延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.performMockRecognition(image: image)
        }
    }
    
    private func performMockRecognition(image: UIImage) {
        // 模拟识别结果
        let mockResults = generateMockResults()
        recognitionResults = mockResults
        isProcessing = false
    }
    
    private func generateMockResults() -> [FoodRecognitionResult] {
        let randomCount = Int.random(in: 1...3)
        var results: [FoodRecognitionResult] = []
        
        let shuffledFoods = mockFoodClassifications.shuffled()
        
        for i in 0..<min(randomCount, shuffledFoods.count) {
            let (englishName, chineseName) = shuffledFoods[i]
            let confidence = Double.random(in: 0.6...0.95)
            
            results.append(FoodRecognitionResult(
                foodName: chineseName,
                englishName: englishName,
                confidence: confidence,
                boundingBox: CGRect(
                    x: Double.random(in: 0.1...0.5),
                    y: Double.random(in: 0.1...0.5),
                    width: Double.random(in: 0.2...0.4),
                    height: Double.random(in: 0.2...0.4)
                )
            ))
        }
        
        return results.sorted { $0.confidence > $1.confidence }
    }
    
    // 实际的 Core ML 识别实现（需要训练好的模型）
    private func performCoreMLRecognition(image: UIImage) {
        guard let cgImage = image.cgImage else {
            errorMessage = "无法处理图片"
            isProcessing = false
            return
        }
        
        // 这里应该加载实际的 Core ML 模型
        // 例如：guard let model = try? VNCoreMLModel(for: FoodClassifier().model) else { return }
        
        let request = VNImageRequestHandler(cgImage: cgImage, options: [:])
        
        // 创建分类请求
        let classificationRequest = VNClassifyImageRequest { [weak self] request, error in
            DispatchQueue.main.async {
                self?.handleClassificationResults(request: request, error: error)
            }
        }
        
        do {
            try request.perform([classificationRequest])
        } catch {
            DispatchQueue.main.async {
                self.errorMessage = "图片识别失败: \(error.localizedDescription)"
                self.isProcessing = false
            }
        }
    }
    
    private func handleClassificationResults(request: VNRequest, error: Error?) {
        if let error = error {
            errorMessage = "识别失败: \(error.localizedDescription)"
            isProcessing = false
            return
        }
        
        guard let observations = request.results as? [VNClassificationObservation] else {
            errorMessage = "无法获取识别结果"
            isProcessing = false
            return
        }
        
        let results = observations.prefix(3).compactMap { observation -> FoodRecognitionResult? in
            guard observation.confidence > 0.5 else { return nil }
            
            let chineseName = translateToChineseName(observation.identifier)
            
            return FoodRecognitionResult(
                foodName: chineseName,
                englishName: observation.identifier,
                confidence: Double(observation.confidence),
                boundingBox: CGRect(x: 0, y: 0, width: 1, height: 1) // 分类任务没有边界框
            )
        }
        
        recognitionResults = results
        isProcessing = false
    }
    
    private func translateToChineseName(_ englishName: String) -> String {
        return mockFoodClassifications[englishName.lowercased()] ?? englishName
    }
    
    func resetResults() {
        recognitionResults = []
        errorMessage = nil
    }
}

struct FoodRecognitionResult: Identifiable {
    let id = UUID()
    let foodName: String
    let englishName: String
    let confidence: Double
    let boundingBox: CGRect
    
    var confidencePercentage: Int {
        Int(confidence * 100)
    }
}

// MARK: - Camera Manager for Food Recognition
@MainActor
class FoodCameraManager: ObservableObject {
    @Published var capturedImage: UIImage?
    @Published var showingImagePicker = false
    @Published var showingCamera = false
    @Published var hasPermission = false
    @Published var errorMessage: String?
    
    init() {
        checkCameraPermission()
    }
    
    func checkCameraPermission() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            hasPermission = true
        case .notDetermined:
            requestCameraPermission()
        case .denied, .restricted:
            hasPermission = false
            errorMessage = "需要相机权限来拍摄食物照片"
        @unknown default:
            hasPermission = false
        }
    }
    
    private func requestCameraPermission() {
        AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
            DispatchQueue.main.async {
                self?.hasPermission = granted
                if !granted {
                    self?.errorMessage = "需要相机权限来拍摄食物照片"
                }
            }
        }
    }
    
    func showImagePicker() {
        showingImagePicker = true
    }
    
    func showCamera() {
        guard hasPermission else {
            errorMessage = "没有相机权限"
            return
        }
        showingCamera = true
    }
    
    func resetImage() {
        capturedImage = nil
    }
}

// MARK: - Image Picker
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.dismiss) private var dismiss
    
    let sourceType: UIImagePickerController.SourceType
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = context.coordinator
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.dismiss()
        }
        
        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.dismiss()
        }
    }
}

// MARK: - Food Camera View
struct FoodCameraView: View {
    let selectedMealType: FoodRecord.MealType
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var nutritionManager: NutritionManager
    
    @StateObject private var cameraManager = FoodCameraManager()
    @StateObject private var recognitionManager = FoodRecognitionManager()
    @State private var showingResults = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                if let image = cameraManager.capturedImage {
                    // 显示拍摄的图片和识别结果
                    ImageRecognitionView(
                        image: image,
                        recognitionManager: recognitionManager,
                        selectedMealType: selectedMealType,
                        onFoodSelected: { record in
                            nutritionManager.addFoodRecord(record)
                            dismiss()
                        }
                    )
                } else {
                    // 拍照选择界面
                    CameraSelectionView(cameraManager: cameraManager)
                }
            }
            .navigationTitle("拍照识别")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                if cameraManager.capturedImage != nil {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("重拍") {
                            cameraManager.resetImage()
                            recognitionManager.resetResults()
                        }
                    }
                }
            }
            .sheet(isPresented: $cameraManager.showingCamera) {
                ImagePicker(
                    selectedImage: $cameraManager.capturedImage,
                    sourceType: .camera
                )
            }
            .sheet(isPresented: $cameraManager.showingImagePicker) {
                ImagePicker(
                    selectedImage: $cameraManager.capturedImage,
                    sourceType: .photoLibrary
                )
            }
            .onChange(of: cameraManager.capturedImage) { image in
                if let image = image {
                    recognitionManager.recognizeFood(from: image)
                }
            }
        }
    }
}

struct CameraSelectionView: View {
    let cameraManager: FoodCameraManager
    
    var body: some View {
        VStack(spacing: 30) {
            Image(systemName: "camera.fill")
                .font(.system(size: 80))
                .foregroundColor(.green)
            
            Text("拍照识别食物")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("使用 AI 技术识别食物并自动获取营养信息")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
            
            VStack(spacing: 16) {
                Button(action: {
                    cameraManager.showCamera()
                }) {
                    HStack {
                        Image(systemName: "camera")
                        Text("拍照")
                    }
                    .font(.headline)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.green)
                    .cornerRadius(12)
                }
                
                Button(action: {
                    cameraManager.showImagePicker()
                }) {
                    HStack {
                        Image(systemName: "photo")
                        Text("从相册选择")
                    }
                    .font(.headline)
                    .foregroundColor(.green)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                }
            }
            .padding(.horizontal)
            
            if let errorMessage = cameraManager.errorMessage {
                Text(errorMessage)
                    .font(.caption)
                    .foregroundColor(.red)
                    .multilineTextAlignment(.center)
                    .padding()
            }
            
            Spacer()
        }
        .padding()
    }
}

struct ImageRecognitionView: View {
    let image: UIImage
    let recognitionManager: FoodRecognitionManager
    let selectedMealType: FoodRecord.MealType
    let onFoodSelected: (FoodRecord) -> Void

    @State private var selectedResult: FoodRecognitionResult?
    @State private var showingFoodDetail = false

    private let foodRepository = FoodRepository()

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 显示拍摄的图片
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxHeight: 300)
                    .cornerRadius(12)
                    .shadow(color: .black.opacity(0.2), radius: 5, x: 0, y: 2)

                // 识别状态
                if recognitionManager.isProcessing {
                    VStack(spacing: 12) {
                        ProgressView()
                            .scaleEffect(1.2)

                        Text("AI 正在识别食物...")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                } else if let errorMessage = recognitionManager.errorMessage {
                    VStack(spacing: 12) {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .font(.title)
                            .foregroundColor(.orange)

                        Text(errorMessage)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding()
                } else if recognitionManager.recognitionResults.isEmpty {
                    VStack(spacing: 12) {
                        Image(systemName: "questionmark.circle.fill")
                            .font(.title)
                            .foregroundColor(.gray)

                        Text("未识别到食物")
                            .font(.subheadline)
                            .foregroundColor(.secondary)

                        Text("请尝试重新拍摄或手动搜索")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    .padding()
                } else {
                    // 识别结果列表
                    RecognitionResultsList(
                        results: recognitionManager.recognitionResults,
                        onResultSelected: { result in
                            selectedResult = result
                            showingFoodDetail = true
                        }
                    )
                }

                Spacer(minLength: 50)
            }
            .padding()
        }
        .sheet(isPresented: $showingFoodDetail) {
            if let result = selectedResult,
               let food = findFoodByName(result.foodName) {
                FoodDetailView(
                    food: food,
                    mealType: selectedMealType,
                    onAddFood: onFoodSelected
                )
            }
        }
    }

    private func findFoodByName(_ name: String) -> Food? {
        let foods = foodRepository.searchFoods(query: name)
        return foods.first { $0.name.contains(name) }
    }
}

struct RecognitionResultsList: View {
    let results: [FoodRecognitionResult]
    let onResultSelected: (FoodRecognitionResult) -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("识别结果")
                .font(.headline)
                .fontWeight(.semibold)

            ForEach(results) { result in
                RecognitionResultRow(result: result) {
                    onResultSelected(result)
                }
            }
        }
    }
}

struct RecognitionResultRow: View {
    let result: FoodRecognitionResult
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // 置信度指示器
                VStack {
                    Text("\(result.confidencePercentage)%")
                        .font(.caption)
                        .fontWeight(.bold)
                        .foregroundColor(confidenceColor)

                    Circle()
                        .fill(confidenceColor)
                        .frame(width: 8, height: 8)
                }
                .frame(width: 40)

                VStack(alignment: .leading, spacing: 4) {
                    Text(result.foodName)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text("置信度: \(result.confidencePercentage)%")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.secondarySystemBackground))
            .cornerRadius(10)
        }
        .buttonStyle(PlainButtonStyle())
    }

    private var confidenceColor: Color {
        switch result.confidencePercentage {
        case 80...100:
            return .green
        case 60..<80:
            return .orange
        default:
            return .red
        }
    }
}

#Preview {
    FoodCameraView(selectedMealType: .lunch)
        .environmentObject(NutritionManager())
}
