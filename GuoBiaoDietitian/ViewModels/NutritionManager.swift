import Foundation
import Combine
import SwiftUI

@MainActor
class NutritionManager: ObservableObject {
    @Published var userProfile: UserProfile?
    @Published var todayRecords: [FoodRecord] = []
    @Published var weeklyRecords: [FoodRecord] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let foodRepository: FoodRepository
    private let userRepository: UserRepository
    private var cancellables = Set<AnyCancellable>()
    
    init(foodRepository: FoodRepository = FoodRepository(),
         userRepository: UserRepository = UserRepository()) {
        self.foodRepository = foodRepository
        self.userRepository = userRepository
        
        loadUserProfile()
        loadTodayRecords()
    }
    
    // MARK: - User Profile Management
    func loadUserProfile() {
        userProfile = userRepository.loadUserProfile()
    }
    
    func updateUserProfile(_ profile: UserProfile) {
        userProfile = profile
        userRepository.saveUserProfile(profile)
    }
    
    // MARK: - Food Records Management
    func loadTodayRecords() {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        
        todayRecords = foodRepository.loadRecords(from: today, to: tomorrow)
    }
    
    func loadWeeklyRecords() {
        let calendar = Calendar.current
        let today = Date()
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: today)!
        
        weeklyRecords = foodRepository.loadRecords(from: weekAgo, to: today)
    }
    
    func addFoodRecord(_ record: FoodRecord) {
        todayRecords.append(record)
        foodRepository.saveRecord(record)
        
        // 触发营养分析更新
        objectWillChange.send()
    }
    
    func deleteFoodRecord(_ record: FoodRecord) {
        todayRecords.removeAll { $0.id == record.id }
        foodRepository.deleteRecord(record.id)
    }
    
    // MARK: - Nutrition Analysis
    var todayNutritionSummary: NutritionSummary {
        calculateNutritionSummary(for: todayRecords)
    }
    
    var dailyRecommendation: ChineseDietaryStandards.DailyRecommendation? {
        guard let profile = userProfile else { return nil }
        return ChineseDietaryStandards.DietaryRecommendations.dailyRecommendation(
            for: profile.gender,
            activityLevel: profile.activityLevel
        )
    }
    
    private func calculateNutritionSummary(for records: [FoodRecord]) -> NutritionSummary {
        let totalNutrition = records.reduce(into: NutritionFacts(
            energy: 0, protein: 0, fat: 0, carbohydrate: 0, sodium: 0,
            saturatedFat: 0, transFat: 0, cholesterol: 0, dietaryFiber: 0, sugar: 0,
            vitaminA: 0, vitaminD: 0, vitaminE: 0, vitaminC: 0,
            thiamine: 0, riboflavin: 0, niacin: 0, vitaminB6: 0, folate: 0, vitaminB12: 0,
            calcium: 0, iron: 0, phosphorus: 0, potassium: 0, magnesium: 0,
            zinc: 0, selenium: 0, copper: 0, manganese: 0, iodine: 0
        )) { total, record in
            let nutrition = record.actualNutrition
            total.energy += nutrition.energy
            total.protein += nutrition.protein
            total.fat += nutrition.fat
            total.carbohydrate += nutrition.carbohydrate
            total.sodium += nutrition.sodium
            // 添加其他营养素...
        }
        
        return NutritionSummary(
            totalNutrition: totalNutrition,
            recommendation: dailyRecommendation,
            mealDistribution: calculateMealDistribution(records)
        )
    }
    
    private func calculateMealDistribution(_ records: [FoodRecord]) -> [FoodRecord.MealType: Double] {
        var distribution: [FoodRecord.MealType: Double] = [:]
        
        for mealType in FoodRecord.MealType.allCases {
            let mealRecords = records.filter { $0.mealType == mealType }
            let totalEnergy = mealRecords.reduce(0) { $0 + $1.actualNutrition.energyKcal }
            distribution[mealType] = totalEnergy
        }
        
        return distribution
    }
    
    // MARK: - Nutrition Status Assessment
    func getNutritionStatus(for nutrient: String, amount: Double) -> NutritionStatus {
        guard let nrv = ChineseDietaryStandards.nrvValues[nutrient] else {
            return .unknown
        }
        
        let percentage = (amount / nrv) * 100
        
        switch percentage {
        case 0..<50:
            return .deficient
        case 50..<80:
            return .insufficient
        case 80..<120:
            return .adequate
        case 120..<150:
            return .high
        default:
            return .excessive
        }
    }
    
    func getMacronutrientBalance() -> MacronutrientBalance? {
        guard let recommendation = dailyRecommendation else { return nil }
        
        let summary = todayNutritionSummary
        let totalEnergy = summary.totalNutrition.energyKcal
        
        guard totalEnergy > 0 else { return nil }
        
        let proteinPercentage = (summary.totalNutrition.protein * 4) / totalEnergy * 100
        let fatPercentage = (summary.totalNutrition.fat * 9) / totalEnergy * 100
        let carbohydratePercentage = (summary.totalNutrition.carbohydrate * 4) / totalEnergy * 100
        
        return MacronutrientBalance(
            proteinPercentage: proteinPercentage,
            fatPercentage: fatPercentage,
            carbohydratePercentage: carbohydratePercentage,
            isBalanced: isBalanced(protein: proteinPercentage, fat: fatPercentage, carb: carbohydratePercentage)
        )
    }
    
    private func isBalanced(protein: Double, fat: Double, carb: Double) -> Bool {
        // 中国膳食指南推荐比例：蛋白质10-15%，脂肪20-30%，碳水化合物50-65%
        return protein >= 10 && protein <= 15 &&
               fat >= 20 && fat <= 30 &&
               carb >= 50 && carb <= 65
    }
}

// MARK: - Supporting Types
struct NutritionSummary {
    let totalNutrition: NutritionFacts
    let recommendation: ChineseDietaryStandards.DailyRecommendation?
    let mealDistribution: [FoodRecord.MealType: Double]
    
    var energyProgress: Double {
        guard let rec = recommendation else { return 0 }
        return totalNutrition.energyKcal / rec.energy
    }
    
    var proteinProgress: Double {
        guard let rec = recommendation else { return 0 }
        return totalNutrition.protein / rec.protein
    }
    
    var fatProgress: Double {
        guard let rec = recommendation else { return 0 }
        return totalNutrition.fat / rec.fat
    }
    
    var carbohydrateProgress: Double {
        guard let rec = recommendation else { return 0 }
        return totalNutrition.carbohydrate / rec.carbohydrate
    }
}

enum NutritionStatus: String, CaseIterable {
    case deficient = "缺乏"
    case insufficient = "不足"
    case adequate = "充足"
    case high = "偏高"
    case excessive = "过量"
    case unknown = "未知"
    
    var color: Color {
        switch self {
        case .deficient, .excessive: return .red
        case .insufficient, .high: return .orange
        case .adequate: return .green
        case .unknown: return .gray
        }
    }
}

struct MacronutrientBalance {
    let proteinPercentage: Double
    let fatPercentage: Double
    let carbohydratePercentage: Double
    let isBalanced: Bool
}
