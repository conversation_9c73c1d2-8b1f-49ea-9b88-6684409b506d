import SwiftUI

struct FoodRecordView: View {
    @EnvironmentObject var nutritionManager: NutritionManager
    @State private var searchText = ""
    @State private var selectedMealType: FoodRecord.MealType = .breakfast
    @State private var showingFoodSearch = false
    @State private var showingBarcodeScanner = false
    @State private var showingCamera = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 餐次选择
                MealTypeSelector(selectedMealType: $selectedMealType)
                
                // 添加食物方式
                FoodInputMethodsView(
                    showingFoodSearch: $showingFoodSearch,
                    showingBarcodeScanner: $showingBarcodeScanner,
                    showingCamera: $showingCamera
                )
                
                // 今日该餐次的记录
                TodayMealRecordsView(mealType: selectedMealType)
                
                Spacer()
            }
            .padding()
            .navigationTitle("记录食物")
            .sheet(isPresented: $showingFoodSearch) {
                FoodSearchView(selectedMealType: selectedMealType)
            }
            .sheet(isPresented: $showingBarcodeScanner) {
                BarcodeScannerView(selectedMealType: selectedMealType)
            }
            .sheet(isPresented: $showingCamera) {
                FoodCameraView(selectedMealType: selectedMealType)
            }
        }
    }
}

struct MealTypeSelector: View {
    @Binding var selectedMealType: FoodRecord.MealType
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("选择餐次")
                .font(.headline)
                .fontWeight(.semibold)
            
            HStack(spacing: 12) {
                ForEach(FoodRecord.MealType.allCases, id: \.self) { mealType in
                    Button(action: {
                        selectedMealType = mealType
                    }) {
                        Text(mealType.rawValue)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                selectedMealType == mealType ?
                                Color.green : Color(.systemGray5)
                            )
                            .foregroundColor(
                                selectedMealType == mealType ?
                                .white : .primary
                            )
                            .cornerRadius(20)
                    }
                }
                
                Spacer()
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct FoodInputMethodsView: View {
    @Binding var showingFoodSearch: Bool
    @Binding var showingBarcodeScanner: Bool
    @Binding var showingCamera: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("添加食物")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                // 搜索食物
                FoodInputMethodButton(
                    icon: "magnifyingglass",
                    title: "搜索食物",
                    subtitle: "从食物数据库中搜索",
                    color: .blue
                ) {
                    showingFoodSearch = true
                }
                
                // 扫描条码
                FoodInputMethodButton(
                    icon: "barcode.viewfinder",
                    title: "扫描条码",
                    subtitle: "扫描包装食品条码",
                    color: .orange
                ) {
                    showingBarcodeScanner = true
                }
                
                // 拍照识别
                FoodInputMethodButton(
                    icon: "camera.fill",
                    title: "拍照识别",
                    subtitle: "AI 识别食物并估算营养",
                    color: .green
                ) {
                    showingCamera = true
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct FoodInputMethodButton: View {
    let icon: String
    let title: String
    let subtitle: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 16) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                    .frame(width: 30)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    Text(subtitle)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.secondarySystemBackground))
            .cornerRadius(10)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct TodayMealRecordsView: View {
    let mealType: FoodRecord.MealType
    @EnvironmentObject var nutritionManager: NutritionManager
    
    private var mealRecords: [FoodRecord] {
        nutritionManager.todayRecords.filter { $0.mealType == mealType }
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("\(mealType.rawValue)记录")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if !mealRecords.isEmpty {
                    Text("共 \(Int(mealRecords.reduce(0) { $0 + $1.actualNutrition.energyKcal })) kcal")
                        .font(.caption)
                        .foregroundColor(.orange)
                        .fontWeight(.medium)
                }
            }
            
            if mealRecords.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "fork.knife.circle")
                        .font(.system(size: 30))
                        .foregroundColor(.secondary)
                    
                    Text("还没有\(mealType.rawValue)记录")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
            } else {
                ForEach(mealRecords) { record in
                    MealRecordRow(record: record)
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct MealRecordRow: View {
    let record: FoodRecord
    @EnvironmentObject var nutritionManager: NutritionManager
    @State private var showingDeleteAlert = false
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(record.food.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack {
                    Text("\(Int(record.amount))g")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    if let brand = record.food.brand {
                        Text("• \(brand)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Text(record.timestamp, style: .time)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            VStack(alignment: .trailing, spacing: 2) {
                Text("\(Int(record.actualNutrition.energyKcal)) kcal")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.orange)
                
                HStack(spacing: 8) {
                    Text("P:\(Int(record.actualNutrition.protein))g")
                        .font(.caption2)
                        .foregroundColor(.blue)
                    
                    Text("F:\(Int(record.actualNutrition.fat))g")
                        .font(.caption2)
                        .foregroundColor(.yellow)
                    
                    Text("C:\(Int(record.actualNutrition.carbohydrate))g")
                        .font(.caption2)
                        .foregroundColor(.green)
                }
            }
            
            Button(action: {
                showingDeleteAlert = true
            }) {
                Image(systemName: "trash")
                    .font(.caption)
                    .foregroundColor(.red)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.vertical, 8)
        .alert("删除记录", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                nutritionManager.deleteFoodRecord(record)
            }
        } message: {
            Text("确定要删除这条食物记录吗？")
        }
    }
}

// MARK: - Placeholder Views (待实现)
struct FoodSearchView: View {
    let selectedMealType: FoodRecord.MealType
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("食物搜索界面")
                    .font(.title2)
                    .foregroundColor(.secondary)
                
                Text("即将实现...")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("搜索食物")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct BarcodeScannerView: View {
    let selectedMealType: FoodRecord.MealType
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("条码扫描界面")
                    .font(.title2)
                    .foregroundColor(.secondary)
                
                Text("即将实现...")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("扫描条码")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct FoodCameraView: View {
    let selectedMealType: FoodRecord.MealType
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("拍照识别界面")
                    .font(.title2)
                    .foregroundColor(.secondary)
                
                Text("即将实现...")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("拍照识别")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct FoodRecordListView: View {
    var body: some View {
        Text("完整食物记录列表")
            .navigationTitle("今日记录")
            .navigationBarTitleDisplayMode(.inline)
    }
}

#Preview {
    FoodRecordView()
        .environmentObject(NutritionManager())
}
