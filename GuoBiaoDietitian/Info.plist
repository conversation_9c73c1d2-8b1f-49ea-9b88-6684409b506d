<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDisplayName</key>
	<string>国标营养师</string>
	<key>CFBundleIdentifier</key>
	<string>com.guobiaodietitian.app</string>
	<key>CFBundleName</key>
	<string>GuoBiaoDietitian</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0</string>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchScreen</key>
	<dict>
		<key>UIColorName</key>
		<string>LaunchScreenBackground</string>
		<key>UIImageName</key>
		<string>LaunchScreenIcon</string>
	</dict>
	<key>UIRequiredDeviceCapabilities</key>
	<array>
		<string>armv7</string>
		<string>healthkit</string>
	</array>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
				</dict>
			</array>
		</dict>
	</dict>
	
	<!-- HealthKit 权限 -->
	<key>NSHealthShareUsageDescription</key>
	<string>需要访问健康数据以同步体重、步数等信息，帮助提供更准确的营养建议</string>
	<key>NSHealthUpdateUsageDescription</key>
	<string>需要写入营养数据到健康应用，以便统一管理您的健康信息</string>
	
	<!-- 相机权限 (用于拍照识别食物) -->
	<key>NSCameraUsageDescription</key>
	<string>需要使用相机拍摄食物照片，以便AI识别并分析营养成分</string>
	
	<!-- 照片库权限 (用于选择食物照片) -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>需要访问照片库以选择食物照片进行营养分析</string>
	
	<!-- 通知权限 -->
	<key>NSUserNotificationsUsageDescription</key>
	<string>需要发送通知提醒您记录饮食和查看营养建议</string>
	
	<!-- 支持的文档类型 (用于数据导入导出) -->
	<key>CFBundleDocumentTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>json</string>
			</array>
			<key>CFBundleTypeIconFiles</key>
			<array/>
			<key>CFBundleTypeName</key>
			<string>营养数据</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.json</string>
			</array>
		</dict>
	</array>
	
	<!-- URL Schemes (用于深度链接) -->
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.guobiaodietitian.app</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>guobiaodietitian</string>
			</array>
		</dict>
	</array>
	
	<!-- 应用传输安全设置 -->
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSExceptionDomains</key>
		<dict>
			<!-- 如果需要访问特定的营养数据API -->
		</dict>
	</dict>
	
	<!-- 后台模式 -->
	<key>UIBackgroundModes</key>
	<array>
		<string>background-processing</string>
		<string>background-fetch</string>
	</array>
	
	<!-- 应用分类 -->
	<key>LSApplicationCategoryType</key>
	<string>public.app-category.healthcare-fitness</string>
	
	<!-- 最低iOS版本 -->
	<key>MinimumOSVersion</key>
	<string>17.0</string>
	
	<!-- 支持的设备 -->
	<key>UIDeviceFamily</key>
	<array>
		<integer>1</integer> <!-- iPhone -->
		<integer>2</integer> <!-- iPad -->
	</array>
	
	<!-- 状态栏样式 -->
	<key>UIStatusBarStyle</key>
	<string>UIStatusBarStyleDefault</string>
	
	<!-- 隐藏状态栏 -->
	<key>UIStatusBarHidden</key>
	<false/>
	
	<!-- 支持暗黑模式 -->
	<key>UIUserInterfaceStyle</key>
	<string>Automatic</string>
</dict>
</plist>
